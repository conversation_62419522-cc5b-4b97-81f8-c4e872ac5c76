import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
  Linking,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { TemperamentType } from '@/types/personality';
import { TEMPERAMENT_COLORS } from '@/constants/temperaments';
import {
  Users,
  Plus,
  X,
  MessageSquare,
  Phone,
  Mail,
  User,
  Briefcase,
  UserCheck,
  Coffee,
  Heart,
  Star,
  ChevronRight,
  CreditCard as Edit3,
  Trash2,
  UserPlus,
  Brain,
  CircleCheck as CheckCircle,
  Search,
} from 'lucide-react-native';
import { useCircleStore, Contact, ContactCategory } from '@/stores/circleStore';

interface ContactAssessment {
  contactId: string;
  currentQuestion: number;
  answers: number[];
  isComplete: boolean;
}

const ASSESSMENT_QUESTIONS = [
  {
    question: 'When making decisions, they usually:',
    answers: [
      'Take charge and decide quickly', // Choleric
      "Consider everyone's feelings first", // Sanguine
      'Analyze all details thoroughly', // Melancholic
      'Seek consensus and avoid conflict', // Phlegmatic
    ],
  },
  {
    question: 'In social situations, they tend to:',
    answers: [
      'Lead conversations and activities', // Choleric
      'Be the life of the party', // Sanguine
      'Listen more than they speak', // Melancholic
      'Go with the flow peacefully', // Phlegmatic
    ],
  },
  {
    question: 'When stressed, they typically:',
    answers: [
      'Become more demanding and direct', // Choleric
      'Try to lighten the mood', // Sanguine
      'Withdraw and think deeply', // Melancholic
      'Remain calm and steady', // Phlegmatic
    ],
  },
  {
    question: 'Their ideal work environment would be:',
    answers: [
      'Fast-paced with clear goals', // Choleric
      'Collaborative and fun', // Sanguine
      'Quiet with time for deep focus', // Melancholic
      'Stable and harmonious', // Phlegmatic
    ],
  },
  {
    question: 'They are motivated most by:',
    answers: [
      'Achievement and recognition', // Choleric
      'Fun and positive relationships', // Sanguine
      'Meaning and personal growth', // Melancholic
      'Security and helping others', // Phlegmatic
    ],
  },
];

const TEMPERAMENT_MAPPING = [
  'choleric',
  'sanguine',
  'melancholic',
  'phlegmatic',
];

export default function CircleScreen() {
  // Get Circle Store state and actions
  const {
    contacts,
    filteredContacts,
    selectedContact: storeSelectedContact,
    activeFilter,
    loadContacts,
    filterContacts,
    addContact: storeAddContact,
    updateContact,
    deleteContact: storeDeleteContact,
    setSelectedContact: storeSelectContact,
    isLoading,
  } = useCircleStore();

  // Local state
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [showAddContact, setShowAddContact] = useState(false);
  const [showContactDetail, setShowContactDetail] = useState(false);
  const [showAssessment, setShowAssessment] = useState(false);
  const [assessment, setAssessment] = useState<ContactAssessment | null>(null);
  const [searchText, setSearchText] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [newContact, setNewContact] = useState({
    name: '',
    email: '',
    phone: '',
    category: 'friends' as ContactCategory,
  });

  const mounted = useRef(true);

  useEffect(() => {
    mounted.current = true;
    loadContacts();

    return () => {
      mounted.current = false;
    };
  }, []);

  // Effect to update local selected contact when store changes
  useEffect(() => {
    if (storeSelectedContact) {
      setSelectedContact(storeSelectedContact);
    }
  }, [storeSelectedContact]);

  // Filter contacts by search text
  const displayedContacts = React.useMemo(() => {
    const source = filteredContacts || contacts;

    if (!searchText.trim()) {
      return source;
    }

    const searchLower = searchText.toLowerCase().trim();
    return source.filter((contact) =>
      contact.name.toLowerCase().includes(searchLower)
    );
  }, [contacts, filteredContacts, searchText]);

  const handleAddContact = async () => {
    if (!newContact.name.trim()) {
      Alert.alert('Error', 'Please enter a contact name');
      return;
    }

    try {
      await storeAddContact({
        name: newContact.name.trim(),
        category: newContact.category,
        notes:
          'Email: ' +
          (newContact.email.trim() || 'N/A') +
          ', Phone: ' +
          (newContact.phone.trim() || 'N/A'),
      });

      setNewContact({
        name: '',
        email: '',
        phone: '',
        category: 'friends',
      });
      setShowAddContact(false);

      // Find the newly added contact (should be the last one)
      const newlyAddedContact = contacts[contacts.length - 1];

      // Ask if they want to take assessment for this contact
      if (newlyAddedContact) {
        Alert.alert(
          'Contact Added!',
          newlyAddedContact.name +
            ' has been added to your circle. Would you like to take a personality assessment for them to get better relationship insights?',
          [
            { text: 'Later', style: 'cancel' },
            {
              text: 'Take Assessment',
              onPress: () => startAssessment(newlyAddedContact),
            },
          ]
        );
      }
    } catch (error: any) {
      console.error('Error adding contact:', error);
      Alert.alert('Error', 'Failed to add contact');
    }
  };

  const startAssessment = (contact: Contact) => {
    storeSelectContact(contact);
    setAssessment({
      contactId: contact.id,
      currentQuestion: 0,
      answers: [],
      isComplete: false,
    });
    setShowAssessment(true);
  };

  const handleAssessmentAnswer = (answerIndex: number) => {
    if (!assessment) return;

    const newAnswers = [...assessment.answers, answerIndex];

    if (assessment.currentQuestion < ASSESSMENT_QUESTIONS.length - 1) {
      setAssessment({
        ...assessment,
        currentQuestion: assessment.currentQuestion + 1,
        answers: newAnswers,
      });
    } else {
      // Assessment complete - calculate results
      completeAssessment(newAnswers);
    }
  };

  const completeAssessment = async (answers: number[]) => {
    if (!selectedContact) return;

    try {
      // Calculate temperament scores
      const scores = [0, 0, 0, 0]; // choleric, sanguine, melancholic, phlegmatic

      answers.forEach((answerIndex) => {
        scores[answerIndex]++;
      });

      const total = answers.length;
      const percentages = scores.map((score) =>
        Math.round((score / total) * 100)
      );

      // Find dominant and secondary temperaments
      const sortedIndices = scores
        .map((score, index) => ({ score, index }))
        .sort((a, b) => b.score - a.score);

      const primaryTemperament = TEMPERAMENT_MAPPING[
        sortedIndices[0].index
      ] as TemperamentType;
      const secondaryTemperament = TEMPERAMENT_MAPPING[
        sortedIndices[1].index
      ] as TemperamentType;

      // Update contact with assessment results
      await updateContact(selectedContact.id, {
        primary_temperament: primaryTemperament,
        secondary_temperament: secondaryTemperament,
      });

      setAssessment({
        ...assessment!,
        isComplete: true,
      });

      // Show results
      Alert.alert(
        'Assessment Complete!',
        selectedContact.name +
          ' appears to be primarily ' +
          (primaryTemperament.charAt(0).toUpperCase() +
            primaryTemperament.slice(1)) +
          ' with ' +
          (secondaryTemperament.charAt(0).toUpperCase() +
            secondaryTemperament.slice(1)) +
          ' traits. This will help you understand how to better connect with them!',
        [
          {
            text: 'Great!',
            onPress: () => {
              setShowAssessment(false);
              setAssessment(null);
              // Refresh contacts to show updated temperament
              loadContacts();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error completing assessment:', error);
      Alert.alert('Error', 'Failed to save assessment results');
    }
  };

  const handleContactPress = (contact: Contact) => {
    storeSelectContact(contact);
    setShowContactDetail(true);
  };

  const handleDeleteContact = (contact: Contact) => {
    Alert.alert(
      'Delete Contact',
      'Are you sure you want to remove ' + contact.name + ' from your circle?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await storeDeleteContact(contact.id);
              setShowContactDetail(false);
              Alert.alert(
                'Success',
                contact.name + ' has been removed from your circle'
              );
            } catch (error) {
              console.error('Error deleting contact:', error);
              Alert.alert('Error', 'Failed to delete contact');
            }
          },
        },
      ]
    );
  };

  const handleCategoryFilter = (category: ContactCategory | null) => {
    if (category === null) {
      filterContacts('all');
    } else {
      filterContacts(category);
    }
    setSearchText('');
  };

  const getTemperamentColor = (temperament: string) => {
    return (
      TEMPERAMENT_COLORS[temperament as keyof typeof TEMPERAMENT_COLORS] ||
      '#6B7280'
    );
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'family':
        return <Heart size={16} color="#EF4444" />;
      case 'work':
        return <Briefcase size={16} color="#3B82F6" />;
      case 'friends':
        return <UserCheck size={16} color="#10B981" />;
      default:
        return <User size={16} color="#6B7280" />;
    }
  };

  const formatTemperamentName = (
    temperament: string | null | undefined
  ): string => {
    if (!temperament || temperament.trim() === '') {
      return 'Unknown';
    }
    return temperament.charAt(0).toUpperCase() + temperament.slice(1);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading your circle...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.title}>Your Circle</Text>
            <Text style={styles.subtitle}>
              Build stronger relationships through personality insights
            </Text>
          </View>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => setShowSearch(!showSearch)}
          >
            <Search size={24} color="#6B7280" />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => setShowAddContact(true)}
          >
            <Plus size={24} color="#ffffff" />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        {showSearch && (
          <View style={styles.searchContainer}>
            <View style={styles.searchInputContainer}>
              <Search size={18} color="#6B7280" />
              <TextInput
                style={styles.searchInput}
                placeholder="Search contacts by name..."
                value={searchText}
                onChangeText={setSearchText}
                autoFocus
              />
              {searchText.length > 0 && (
                <TouchableOpacity onPress={() => setSearchText('')}>
                  <X size={18} color="#6B7280" />
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}

        {/* Category Filters */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoryFiltersContainer}
        >
          <TouchableOpacity
            style={[
              styles.categoryFilterButton,
              activeFilter === 'all' && styles.categoryFilterButtonActive,
            ]}
            onPress={() => handleCategoryFilter(null)}
          >
            <Users
              size={16}
              color={activeFilter === 'all' ? '#3B82F6' : '#6B7280'}
            />
            <Text
              style={[
                styles.categoryFilterText,
                activeFilter === 'all' && styles.categoryFilterTextActive,
              ]}
            >
              All
            </Text>
          </TouchableOpacity>

          {(['family', 'friends', 'work', 'other'] as ContactCategory[]).map(
            (category) => (
              <TouchableOpacity
                key={category}
                style={[
                  styles.categoryFilterButton,
                  activeFilter === category &&
                    styles.categoryFilterButtonActive,
                ]}
                onPress={() => handleCategoryFilter(category)}
              >
                {getCategoryIcon(category)}
                <Text
                  style={[
                    styles.categoryFilterText,
                    activeFilter === category &&
                      styles.categoryFilterTextActive,
                  ]}
                >
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </Text>
              </TouchableOpacity>
            )
          )}
        </ScrollView>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Users size={24} color="#3B82F6" />
            <Text style={styles.statNumber}>{displayedContacts.length}</Text>
            <Text style={styles.statLabel}>Contacts</Text>
          </View>
          <View style={styles.statCard}>
            <Brain size={24} color="#8B5CF6" />
            <Text style={styles.statNumber}>
              {displayedContacts.filter((c) => c.primary_temperament).length}
            </Text>
            <Text style={styles.statLabel}>Assessed</Text>
          </View>
          <View style={styles.statCard}>
            <Star size={24} color="#F59E0B" />
            <Text style={styles.statNumber}>
              {Math.round(
                (displayedContacts.filter((c) => c.primary_temperament).length /
                  Math.max(displayedContacts.length, 1)) *
                  100
              )}
              %
            </Text>
            <Text style={styles.statLabel}>Complete</Text>
          </View>
        </View>

        {/* Contacts Grid */}
        <View style={styles.contactsSection}>
          <Text style={styles.sectionTitle}>
            {activeFilter !== 'all'
              ? `${
                  activeFilter.charAt(0).toUpperCase() + activeFilter.slice(1)
                } Contacts`
              : 'Your People'}
            {searchText && ' (Search: "' + searchText + '")'}
          </Text>

          {displayedContacts.length === 0 ? (
            <View style={styles.emptyState}>
              <Users size={48} color="#D1D5DB" />
              <Text style={styles.emptyTitle}>
                {searchText
                  ? 'No matching contacts'
                  : activeFilter !== 'all'
                  ? 'No ' + activeFilter + ' contacts yet'
                  : 'No contacts yet'}
              </Text>
              <Text style={styles.emptyText}>
                {searchText
                  ? `Try a different search term or clear your search`
                  : `Add people to your circle to get personalized relationship insights and better understand how to connect with them`}
              </Text>
              {!searchText && (
                <TouchableOpacity
                  style={styles.emptyButton}
                  onPress={() => setShowAddContact(true)}
                >
                  <UserPlus size={20} color="#ffffff" />
                  <Text style={styles.emptyButtonText}>
                    {activeFilter !== 'all'
                      ? 'Add a ' + activeFilter + ' Contact'
                      : 'Add Your First Contact'}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <View style={styles.contactsGrid}>
              {displayedContacts.map((contact) => (
                <TouchableOpacity
                  key={contact.id}
                  style={styles.contactCard}
                  onPress={() => handleContactPress(contact)}
                >
                  <View style={styles.contactHeader}>
                    <View style={styles.contactCategory}>
                      {getCategoryIcon(contact.category)}
                      <Text style={styles.contactCategoryText}>
                        {contact.category.charAt(0).toUpperCase() +
                          contact.category.slice(1)}
                      </Text>
                    </View>
                    {contact.primary_temperament && (
                      <View style={styles.assessmentBadge}>
                        <CheckCircle size={12} color="#10B981" />
                      </View>
                    )}
                  </View>

                  <Text style={styles.contactName}>{contact.name}</Text>

                  {contact.notes && (
                    <Text style={styles.contactDetail} numberOfLines={1}>
                      {contact.notes.length > 25
                        ? contact.notes.substring(0, 25) + '...'
                        : contact.notes}
                    </Text>
                  )}

                  {contact.primary_temperament ? (
                    <View style={styles.contactTemperament}>
                      <View
                        style={[
                          styles.temperamentDot,
                          {
                            backgroundColor: getTemperamentColor(
                              contact.primary_temperament
                            ),
                          },
                        ]}
                      />
                      <Text style={styles.temperamentText}>
                        {formatTemperamentName(contact.primary_temperament)}
                      </Text>
                    </View>
                  ) : (
                    <TouchableOpacity
                      style={styles.assessmentPrompt}
                      onPress={(e) => {
                        e.stopPropagation();
                        startAssessment(contact);
                      }}
                    >
                      <Brain size={14} color="#8B5CF6" />
                      <Text style={styles.assessmentPromptText}>
                        Take Assessment
                      </Text>
                    </TouchableOpacity>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </ScrollView>

      {/* Add Contact Modal */}
      <Modal
        visible={showAddContact}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAddContact(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add to Circle</Text>
              <TouchableOpacity onPress={() => setShowAddContact(false)}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Name *</Text>
                <TextInput
                  style={styles.formInput}
                  placeholder="Enter their name"
                  value={newContact.name}
                  onChangeText={(text) =>
                    setNewContact((prev) => ({ ...prev, name: text }))
                  }
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Email (Optional)</Text>
                <TextInput
                  style={styles.formInput}
                  placeholder="Enter their email"
                  value={newContact.email}
                  onChangeText={(text) =>
                    setNewContact((prev) => ({ ...prev, email: text }))
                  }
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Phone (Optional)</Text>
                <TextInput
                  style={styles.formInput}
                  placeholder="Enter their phone number"
                  value={newContact.phone}
                  onChangeText={(text) =>
                    setNewContact((prev) => ({ ...prev, phone: text }))
                  }
                  keyboardType="phone-pad"
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Category</Text>
                <View style={styles.categoryButtons}>
                  {(
                    ['family', 'work', 'friends', 'other'] as ContactCategory[]
                  ).map((category) => (
                    <TouchableOpacity
                      key={category}
                      style={[
                        styles.categoryButton,
                        newContact.category === category &&
                          styles.categoryButtonActive,
                      ]}
                      onPress={() =>
                        setNewContact((prev) => ({ ...prev, category }))
                      }
                    >
                      {getCategoryIcon(category)}
                      <Text
                        style={[
                          styles.categoryButtonText,
                          newContact.category === category &&
                            styles.categoryButtonTextActive,
                        ]}
                      >
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.formHint}>
                <Text style={styles.formHintText}>
                  💡 After adding them, you can take a personality assessment to
                  better understand how to connect with them
                </Text>
              </View>
            </ScrollView>

            <TouchableOpacity
              style={styles.submitButton}
              onPress={handleAddContact}
            >
              <Text style={styles.submitButtonText}>Add to Circle</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Contact Detail Modal */}
      <Modal
        visible={showContactDetail}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowContactDetail(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {selectedContact && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>{selectedContact.name}</Text>
                  <TouchableOpacity onPress={() => setShowContactDetail(false)}>
                    <X size={24} color="#6B7280" />
                  </TouchableOpacity>
                </View>

                <ScrollView showsVerticalScrollIndicator={false}>
                  <View style={styles.contactDetailInfo}>
                    <View style={styles.contactDetailRow}>
                      <View style={styles.contactDetailCategory}>
                        {getCategoryIcon(selectedContact.category)}
                        <Text style={styles.contactDetailCategoryText}>
                          {selectedContact.category.charAt(0).toUpperCase() +
                            selectedContact.category.slice(1)}
                        </Text>
                      </View>
                    </View>

                    {selectedContact.notes && (
                      <View style={styles.contactDetailRow}>
                        <MessageSquare size={16} color="#6B7280" />
                        <Text style={styles.contactDetailText}>
                          {selectedContact.notes}
                        </Text>
                      </View>
                    )}

                    {selectedContact.primary_temperament ? (
                      <View style={styles.personalitySection}>
                        <Text style={styles.personalitySectionTitle}>
                          Personality Profile
                        </Text>
                        <View style={styles.contactDetailTemperament}>
                          <View
                            style={[
                              styles.temperamentDot,
                              {
                                backgroundColor: getTemperamentColor(
                                  selectedContact.primary_temperament
                                ),
                              },
                            ]}
                          />
                          <View style={styles.temperamentInfo}>
                            <Text style={styles.contactDetailTemperamentText}>
                              {formatTemperamentName(
                                selectedContact.primary_temperament
                              )}{' '}
                              Personality
                            </Text>
                            <Text style={styles.temperamentDescription}>
                              {selectedContact.primary_temperament ===
                                'choleric' &&
                                'Natural leader, goal-oriented, decisive'}
                              {selectedContact.primary_temperament ===
                                'sanguine' &&
                                'Enthusiastic, social, optimistic'}
                              {selectedContact.primary_temperament ===
                                'melancholic' &&
                                'Thoughtful, analytical, creative'}
                              {selectedContact.primary_temperament ===
                                'phlegmatic' && 'Calm, peaceful, supportive'}
                            </Text>
                          </View>
                        </View>

                        <TouchableOpacity
                          style={styles.retakeAssessmentButton}
                          onPress={() => {
                            setShowContactDetail(false);
                            startAssessment(selectedContact);
                          }}
                        >
                          <Brain size={16} color="#8B5CF6" />
                          <Text style={styles.retakeAssessmentText}>
                            Retake Assessment
                          </Text>
                        </TouchableOpacity>
                      </View>
                    ) : (
                      <View style={styles.assessmentSection}>
                        <Text style={styles.assessmentSectionTitle}>
                          Personality Assessment
                        </Text>
                        <Text style={styles.assessmentSectionText}>
                          Take a quick 5-question assessment to understand their
                          personality and get insights on how to better connect
                          with them.
                        </Text>
                        <TouchableOpacity
                          style={styles.takeAssessmentButton}
                          onPress={() => {
                            setShowContactDetail(false);
                            startAssessment(selectedContact);
                          }}
                        >
                          <Brain size={20} color="#ffffff" />
                          <Text style={styles.takeAssessmentText}>
                            Take Assessment
                          </Text>
                        </TouchableOpacity>
                      </View>
                    )}
                  </View>
                </ScrollView>

                <View style={styles.contactActions}>
                  <TouchableOpacity
                    style={styles.deleteContactButton}
                    onPress={() => handleDeleteContact(selectedContact)}
                  >
                    <Trash2 size={16} color="#EF4444" />
                    <Text style={styles.deleteContactText}>
                      Remove from Circle
                    </Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>

      {/* Assessment Modal */}
      <Modal
        visible={showAssessment}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowAssessment(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {assessment && selectedContact && (
              <>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>
                    Assessment for {selectedContact.name}
                  </Text>
                  <TouchableOpacity onPress={() => setShowAssessment(false)}>
                    <X size={24} color="#6B7280" />
                  </TouchableOpacity>
                </View>

                <View style={styles.assessmentProgress}>
                  <Text style={styles.progressText}>
                    Question {assessment.currentQuestion + 1} of{' '}
                    {ASSESSMENT_QUESTIONS.length}
                  </Text>
                  <View style={styles.progressBar}>
                    <View
                      style={[
                        styles.progressFill,
                        {
                          width: `${
                            ((assessment.currentQuestion + 1) /
                              ASSESSMENT_QUESTIONS.length) *
                            100
                          }%`,
                        },
                      ]}
                    />
                  </View>
                </View>

                <View style={styles.questionContainer}>
                  <Text style={styles.questionText}>
                    {ASSESSMENT_QUESTIONS[assessment.currentQuestion].question}
                  </Text>
                </View>

                <View style={styles.answersContainer}>
                  {ASSESSMENT_QUESTIONS[assessment.currentQuestion].answers.map(
                    (answer, index) => (
                      <TouchableOpacity
                        key={index}
                        style={styles.answerButton}
                        onPress={() => handleAssessmentAnswer(index)}
                      >
                        <View style={styles.answerContent}>
                          <View style={styles.answerIndicator}>
                            <Text style={styles.answerLetter}>
                              {String.fromCharCode(65 + index)}
                            </Text>
                          </View>
                          <Text style={styles.answerText}>{answer}</Text>
                        </View>
                      </TouchableOpacity>
                    )
                  )}
                </View>
              </>
            )}
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  headerContent: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  actionButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    marginRight: 8,
  },
  addButton: {
    backgroundColor: '#3B82F6',
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    paddingVertical: 4,
  },
  categoryFiltersContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
    gap: 8,
  },
  categoryFilterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    gap: 6,
  },
  categoryFilterButtonActive: {
    backgroundColor: '#EBF5FF',
  },
  categoryFilterText: {
    fontSize: 14,
    color: '#6B7280',
  },
  categoryFilterTextActive: {
    color: '#3B82F6',
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  contactsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
    gap: 12,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
  },
  emptyText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  emptyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  emptyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  contactsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  contactCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    width: '48%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  contactHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  contactCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  contactCategoryText: {
    fontSize: 12,
    color: '#6B7280',
    textTransform: 'capitalize',
  },
  assessmentBadge: {
    backgroundColor: '#F0FDF4',
    borderRadius: 10,
    padding: 4,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  contactDetail: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 2,
  },
  contactTemperament: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginTop: 8,
  },
  temperamentDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  temperamentText: {
    fontSize: 12,
    color: '#6B7280',
  },
  assessmentPrompt: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginTop: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 6,
  },
  assessmentPromptText: {
    fontSize: 11,
    color: '#8B5CF6',
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  formGroup: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 8,
  },
  formInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  categoryButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    gap: 6,
  },
  categoryButtonActive: {
    backgroundColor: '#EBF8FF',
    borderColor: '#3B82F6',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#6B7280',
  },
  categoryButtonTextActive: {
    color: '#3B82F6',
    fontWeight: '500',
  },
  formHint: {
    backgroundColor: '#F0F9FF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
  },
  formHintText: {
    fontSize: 14,
    color: '#1E40AF',
    lineHeight: 20,
  },
  submitButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  contactDetailInfo: {
    marginBottom: 24,
  },
  contactDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12,
  },
  contactDetailCategory: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  contactDetailCategoryText: {
    fontSize: 16,
    color: '#6B7280',
    textTransform: 'capitalize',
  },
  contactDetailText: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  personalitySection: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  personalitySectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  contactDetailTemperament: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 12,
  },
  temperamentInfo: {
    flex: 1,
  },
  contactDetailTemperamentText: {
    fontSize: 16,
    color: '#1F2937',
    fontWeight: '500',
  },
  temperamentDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  retakeAssessmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    alignSelf: 'flex-start',
  },
  retakeAssessmentText: {
    fontSize: 14,
    color: '#8B5CF6',
    fontWeight: '500',
  },
  assessmentSection: {
    backgroundColor: '#F0F9FF',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  assessmentSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E40AF',
    marginBottom: 8,
  },
  assessmentSectionText: {
    fontSize: 14,
    color: '#1E40AF',
    lineHeight: 20,
    marginBottom: 16,
  },
  takeAssessmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  takeAssessmentText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  contactActions: {
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
    paddingTop: 16,
  },
  deleteContactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 12,
    borderRadius: 8,
    backgroundColor: '#FEF2F2',
  },
  deleteContactText: {
    fontSize: 16,
    color: '#EF4444',
    fontWeight: '500',
  },
  assessmentProgress: {
    marginBottom: 24,
  },
  progressText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  progressBar: {
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 2,
  },
  questionContainer: {
    marginBottom: 32,
  },
  questionText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    lineHeight: 28,
  },
  answersContainer: {
    gap: 12,
  },
  answerButton: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  answerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 12,
  },
  answerIndicator: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  answerLetter: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  answerText: {
    flex: 1,
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 22,
  },
});
