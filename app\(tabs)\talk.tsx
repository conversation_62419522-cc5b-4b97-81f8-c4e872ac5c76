import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { supabase } from '@/lib/supabase';
import { openRouterService } from '@/lib/openrouter';
import {
  PersonalityProfile,
  CircleContact,
  MoodEntry,
  UserProfile,
} from '@/types/personality';
import {
  Send,
  Bot,
  User,
  Sparkles,
  Brain,
  Heart,
  Users,
  MessageCircle,
  Loader,
} from 'lucide-react-native';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isTyping?: boolean;
}

interface UserContext {
  profile: UserProfile | null;
  personality: PersonalityProfile | null;
  contacts: CircleContact[];
  recentMoods: MoodEntry[];
  contactsWithPersonality: CircleContact[];
}

export default function TalkScreen() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [userContext, setUserContext] = useState<UserContext>({
    profile: null,
    personality: null,
    contacts: [],
    recentMoods: [],
    contactsWithPersonality: [],
  });
  const scrollViewRef = useRef<ScrollView>(null);
  const mounted = useRef(true);

  useEffect(() => {
    mounted.current = true;
    loadUserContext();
    addWelcomeMessage();

    return () => {
      mounted.current = false;
    };
  }, []);

  const loadUserContext = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user || !mounted.current) return;

      // Load user profile
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      // Load personality profile
      const { data: personality } = await supabase
        .from('personality_profiles')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      // Load circle contacts
      const { data: contacts } = await supabase
        .from('circle_contacts')
        .select('*')
        .eq('user_id', user.id)
        .order('name');

      // Load recent mood entries
      const { data: recentMoods } = await supabase
        .from('mood_entries')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (mounted.current) {
        const contactsWithPersonality =
          contacts?.filter((c) => c.dominant_temperament) || [];

        setUserContext({
          profile: profile || null,
          personality: personality || null,
          contacts: contacts || [],
          recentMoods: recentMoods || [],
          contactsWithPersonality,
        });
      }
    } catch (error) {
      console.error('Error loading user context:', error);
    }
  };

  const addWelcomeMessage = () => {
    const welcomeMessage: Message = {
      id: 'welcome',
      content:
        "Hi there! I'm your personal AI assistant. I have access to your personality profile, circle contacts, and mood history to give you personalized advice. Ask me anything about relationships, personality insights, or how to better connect with people in your circle!",
      isUser: false,
      timestamp: new Date(),
    };
    setMessages([welcomeMessage]);
  };

  const buildContextPrompt = (userQuestion: string): string => {
    const {
      profile,
      personality,
      contacts,
      recentMoods,
      contactsWithPersonality,
    } = userContext;

    let contextPrompt = `You are a wise, empathetic AI assistant specializing in personality psychology and relationships. You have access to the user's personal data to provide highly personalized advice.

USER CONTEXT:
`;

    if (profile) {
      contextPrompt +=
        '- Name: ' +
        (profile.full_name || 'User') +
        '\n' +
        '- Email verified: ' +
        (profile.email_verified ? 'Yes' : 'No') +
        '\n' +
        '- Assessment completed: ' +
        (profile.has_completed_assessment ? 'Yes' : 'No') +
        '\n';
    }

    if (personality) {
      contextPrompt +=
        '\n' +
        'PERSONALITY PROFILE:\n' +
        '- Dominant temperament: ' +
        personality.dominant_temperament +
        ' (' +
        personality.dominant_percentage +
        '%)\n' +
        '- Secondary temperament: ' +
        personality.secondary_temperament +
        ' (' +
        personality.secondary_percentage +
        '%)\n' +
        '- Assessment completed: ' +
        new Date(personality.assessment_completed_at).toLocaleDateString() +
        '\n';
    }

    if (contacts.length > 0) {
      contextPrompt +=
        '\n' + 'CIRCLE CONTACTS (' + contacts.length + ' total):\n';
      contacts.forEach((contact) => {
        contextPrompt += '- ' + contact.name + ' (' + contact.category + ')';
        if (contact.dominant_temperament) {
          contextPrompt +=
            ' - ' + contact.dominant_temperament + ' temperament';
        }
        contextPrompt += '\n';
      });
    }

    if (contactsWithPersonality.length > 0) {
      contextPrompt += `
CONTACTS WITH KNOWN PERSONALITIES:
`;
      contactsWithPersonality.forEach((contact) => {
        contextPrompt += `- ${contact.name}: ${contact.dominant_temperament}`;
        if (contact.secondary_temperament) {
          contextPrompt += ` (secondary: ${contact.secondary_temperament})`;
        }
        contextPrompt += '\n';
      });
    }

    if (recentMoods.length > 0) {
      contextPrompt += `
RECENT MOOD HISTORY:
`;
      recentMoods.slice(0, 5).forEach((mood) => {
        const date = new Date(mood.created_at).toLocaleDateString();
        contextPrompt += `- ${date}: ${mood.emoji}`;
        if (mood.description) {
          contextPrompt += ` (${mood.description})`;
        }
        contextPrompt += '\n';
      });
    }

    contextPrompt += `
TEMPERAMENT CHARACTERISTICS:
- Choleric: Natural leaders, decisive, goal-oriented, direct, ambitious
- Sanguine: Enthusiastic, social, optimistic, spontaneous, people-focused  
- Melancholic: Thoughtful, analytical, creative, introspective, detail-oriented
- Phlegmatic: Calm, peaceful, supportive, diplomatic, steady

INSTRUCTIONS:
1. Provide personalized advice based on the user's specific temperament and situation
2. When discussing relationships, reference specific contacts by name when relevant
3. Consider temperament compatibility when giving relationship advice
4. Use the user's mood history to provide contextually appropriate responses
5. Be warm, empathetic, and encouraging
6. Keep responses conversational and practical
7. If asked about specific contacts, use their known temperaments to give targeted advice

USER QUESTION: ${userQuestion}

Provide a helpful, personalized response based on all the context above:`;

    return contextPrompt;
  };

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputText.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    const typingMessage: Message = {
      id: 'typing',
      content: '',
      isUser: false,
      timestamp: new Date(),
      isTyping: true,
    };

    setMessages((prev) => [...prev, userMessage, typingMessage]);
    setInputText('');
    setIsLoading(true);

    // Scroll to bottom
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      if (!openRouterService.isConfigured()) {
        throw new Error(
          'AI service not configured. Please check your OpenRouter API key.'
        );
      }

      const contextPrompt = buildContextPrompt(userMessage.content);

      const response = await fetch(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${process.env.EXPO_PUBLIC_OPENROUTER_API_KEY}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://harmona.app',
            'X-Title': 'Harmona AI Assistant',
          },
          body: JSON.stringify({
            model: 'google/gemini-2.5-flash-preview-05-20',
            messages: [
              {
                role: 'system',
                content:
                  "You are a wise, empathetic AI assistant specializing in personality psychology and relationships. Provide helpful, personalized advice based on the user's context.",
              },
              {
                role: 'user',
                content: contextPrompt,
              },
            ],
            temperature: 0.7,
            max_tokens: 1000,
            top_p: 0.9,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`AI service error: ${response.status}`);
      }

      const data = await response.json();
      const aiResponse = data.choices[0]?.message?.content;

      if (!aiResponse) {
        throw new Error('No response from AI service');
      }

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: aiResponse,
        isUser: false,
        timestamp: new Date(),
      };

      if (mounted.current) {
        setMessages((prev) =>
          prev.filter((m) => m.id !== 'typing').concat(aiMessage)
        );
      }
    } catch (error: any) {
      console.error('Error getting AI response:', error);

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `I apologize, but I'm having trouble connecting to my AI service right now. ${
          error.message.includes('not configured')
            ? 'The AI service needs to be configured first.'
            : 'Please try again in a moment.'
        }`,
        isUser: false,
        timestamp: new Date(),
      };

      if (mounted.current) {
        setMessages((prev) =>
          prev.filter((m) => m.id !== 'typing').concat(errorMessage)
        );
      }
    } finally {
      if (mounted.current) {
        setIsLoading(false);
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderMessage = (message: Message) => {
    if (message.isTyping) {
      return (
        <View
          key={message.id}
          style={[styles.messageContainer, styles.aiMessageContainer]}
        >
          <View style={styles.messageAvatar}>
            <Bot size={20} color="#3B82F6" />
          </View>
          <View style={[styles.messageBubble, styles.aiMessageBubble]}>
            <View style={styles.typingIndicator}>
              <View style={[styles.typingDot, { animationDelay: '0ms' }]} />
              <View style={[styles.typingDot, { animationDelay: '150ms' }]} />
              <View style={[styles.typingDot, { animationDelay: '300ms' }]} />
            </View>
          </View>
        </View>
      );
    }

    return (
      <View
        key={message.id}
        style={[
          styles.messageContainer,
          message.isUser
            ? styles.userMessageContainer
            : styles.aiMessageContainer,
        ]}
      >
        <View style={styles.messageAvatar}>
          {message.isUser ? (
            <User size={20} color="#ffffff" />
          ) : (
            <Bot size={20} color="#3B82F6" />
          )}
        </View>
        <View style={styles.messageContent}>
          <View
            style={[
              styles.messageBubble,
              message.isUser
                ? styles.userMessageBubble
                : styles.aiMessageBubble,
            ]}
          >
            <Text
              style={[
                styles.messageText,
                message.isUser ? styles.userMessageText : styles.aiMessageText,
              ]}
            >
              {message.content}
            </Text>
          </View>
          <Text style={styles.messageTime}>
            {formatTime(message.timestamp)}
          </Text>
        </View>
      </View>
    );
  };

  const getSuggestionQuestions = () => {
    const { personality, contacts, contactsWithPersonality } = userContext;

    const suggestions = [
      'How can I better understand my personality type?',
      'What are my strengths and potential challenges?',
    ];

    if (contacts.length > 0) {
      suggestions.push(
        'How can I improve my relationships with people in my circle?'
      );
    }

    if (contactsWithPersonality.length > 0) {
      const contact = contactsWithPersonality[0];
      suggestions.push(
        `How should I communicate with ${contact.name} based on their ${contact.dominant_temperament} personality?`
      );
    }

    if (personality) {
      suggestions.push(
        `As a ${personality.dominant_temperament}, what should I focus on for personal growth?`
      );
    }

    return suggestions.slice(0, 4);
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <View style={styles.headerIcon}>
              <MessageCircle size={24} color="#3B82F6" />
            </View>
            <View>
              <Text style={styles.headerTitle}>AI Assistant</Text>
              <Text style={styles.headerSubtitle}>
                Personalized advice based on your personality
              </Text>
            </View>
          </View>
          <View style={styles.headerStats}>
            <View style={styles.statItem}>
              <Brain size={16} color="#8B5CF6" />
              <Text style={styles.statText}>
                {userContext.personality ? 'Assessed' : 'No Assessment'}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Users size={16} color="#10B981" />
              <Text style={styles.statText}>
                {userContext.contacts.length} contacts
              </Text>
            </View>
          </View>
        </View>

        {/* Messages */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          contentContainerStyle={styles.messagesContent}
          showsVerticalScrollIndicator={false}
        >
          {messages.map(renderMessage)}

          {/* Suggestion Questions */}
          {messages.length <= 1 && (
            <View style={styles.suggestionsContainer}>
              <Text style={styles.suggestionsTitle}>Try asking me:</Text>
              {getSuggestionQuestions().map((suggestion, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestionButton}
                  onPress={() => setInputText(suggestion)}
                >
                  <Sparkles size={16} color="#3B82F6" />
                  <Text style={styles.suggestionText}>{suggestion}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </ScrollView>

        {/* Input Area */}
        <View style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <TextInput
              style={styles.textInput}
              placeholder="Ask me anything about personality or relationships..."
              value={inputText}
              onChangeText={setInputText}
              multiline
              maxLength={500}
              editable={!isLoading}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                (!inputText.trim() || isLoading) && styles.sendButtonDisabled,
              ]}
              onPress={handleSendMessage}
              disabled={!inputText.trim() || isLoading}
            >
              {isLoading ? (
                <Loader size={20} color="#ffffff" />
              ) : (
                <Send size={20} color="#ffffff" />
              )}
            </TouchableOpacity>
          </View>

          {!openRouterService.isConfigured() && (
            <View style={styles.configWarning}>
              <Text style={styles.configWarningText}>
                ⚠️ AI service not configured. Some features may be limited.
              </Text>
            </View>
          )}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  keyboardAvoid: {
    flex: 1,
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#EBF8FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  headerStats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 20,
    paddingBottom: 10,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'flex-start',
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  aiMessageContainer: {
    justifyContent: 'flex-start',
  },
  messageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#3B82F6',
    marginRight: 8,
  },
  messageContent: {
    flex: 1,
    maxWidth: '80%',
  },
  messageBubble: {
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 4,
  },
  userMessageBubble: {
    backgroundColor: '#3B82F6',
    alignSelf: 'flex-end',
  },
  aiMessageBubble: {
    backgroundColor: '#ffffff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  userMessageText: {
    color: '#ffffff',
  },
  aiMessageText: {
    color: '#1F2937',
  },
  messageTime: {
    fontSize: 12,
    color: '#9CA3AF',
    alignSelf: 'flex-end',
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingVertical: 4,
  },
  typingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#9CA3AF',
  },
  suggestionsContainer: {
    marginTop: 20,
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  suggestionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  suggestionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    marginBottom: 8,
    gap: 8,
  },
  suggestionText: {
    flex: 1,
    fontSize: 14,
    color: '#4B5563',
  },
  inputContainer: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: '#F9FAFB',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    maxHeight: 100,
    paddingVertical: 8,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#3B82F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  configWarning: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#FEF3C7',
    borderRadius: 6,
  },
  configWarningText: {
    fontSize: 12,
    color: '#92400E',
    textAlign: 'center',
  },
});
