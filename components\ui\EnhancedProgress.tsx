import React, { useEffect, useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  AccessibilityInfo,
} from 'react-native';
import { Clock, CheckCircle } from 'lucide-react-native';

interface EnhancedProgressProps {
  currentStep: number;
  totalSteps: number;
  phase: 'initial' | 'confirmation' | 'comparison' | 'complete';
  estimatedTimeRemaining?: number; // in seconds
  showTimeEstimate?: boolean;
  accessibilityLabel?: string;
}

export const EnhancedProgress: React.FC<EnhancedProgressProps> = ({
  currentStep,
  totalSteps,
  phase,
  estimatedTimeRemaining,
  showTimeEstimate = true,
  accessibilityLabel,
}) => {
  const [animatedProgress] = useState(new Animated.Value(0));
  const [isReduceMotionEnabled, setIsReduceMotionEnabled] = useState(false);

  // Check for reduced motion preference
  useEffect(() => {
    const checkReduceMotion = async () => {
      const isEnabled = await AccessibilityInfo.isReduceMotionEnabled();
      setIsReduceMotionEnabled(isEnabled);
    };
    checkReduceMotion();
  }, []);

  const progressPercentage = useMemo(() => {
    return totalSteps > 0 ? Math.round((currentStep / totalSteps) * 100) : 0;
  }, [currentStep, totalSteps]);

  const phaseInfo = useMemo(() => {
    const phases = {
      initial: { label: 'Initial Assessment', color: '#3B82F6', steps: '1-4' },
      confirmation: { label: 'Confirmation', color: '#F59E0B', steps: '5-6' },
      comparison: {
        label: 'Final Comparison',
        color: '#10B981',
        steps: '7-10',
      },
      complete: { label: 'Complete', color: '#10B981', steps: 'Done' },
    };
    return phases[phase] || phases.initial;
  }, [phase]);

  const formatTimeEstimate = (seconds: number): string => {
    // Safety check for invalid input
    if (!seconds || isNaN(seconds) || seconds < 0) {
      return '0s remaining';
    }

    const roundedSeconds = Math.round(seconds);
    if (roundedSeconds < 60) {
      return roundedSeconds + 's remaining';
    }
    const minutes = Math.floor(roundedSeconds / 60);
    const remainingSeconds = roundedSeconds % 60;
    if (remainingSeconds === 0) {
      return minutes + 'm remaining';
    }
    return minutes + 'm ' + remainingSeconds + 's remaining';
  };

  // Animate progress bar
  useEffect(() => {
    const roundedProgress = Math.round(progressPercentage);
    if (isReduceMotionEnabled) {
      animatedProgress.setValue(roundedProgress);
    } else {
      Animated.timing(animatedProgress, {
        toValue: roundedProgress,
        duration: 500,
        useNativeDriver: false,
      }).start();
    }
  }, [progressPercentage, animatedProgress, isReduceMotionEnabled]);

  const accessibilityText =
    accessibilityLabel ||
    'Progress: ' +
      (currentStep || 0) +
      ' of ' +
      (totalSteps || 0) +
      ' questions completed. ' +
      (phaseInfo.label || 'Assessment') +
      ' phase. ' +
      (estimatedTimeRemaining && showTimeEstimate && estimatedTimeRemaining > 0
        ? formatTimeEstimate(estimatedTimeRemaining)
        : '');

  return (
    <View
      style={styles.container}
      accessible={true}
      accessibilityRole="progressbar"
      accessibilityLabel={accessibilityText}
      accessibilityValue={{
        min: 0,
        max: totalSteps,
        now: currentStep,
      }}
    >
      {/* Phase and step info */}
      <View style={styles.headerRow}>
        <View style={styles.stepInfo}>
          <Text style={styles.stepText}>
            {currentStep || 0} of {totalSteps || 0}
          </Text>
          <View
            style={[
              styles.phaseBadge,
              { backgroundColor: (phaseInfo.color || '#3B82F6') + '15' },
            ]}
          >
            <Text
              style={[
                styles.phaseText,
                { color: phaseInfo.color || '#3B82F6' },
              ]}
            >
              {phaseInfo.label || 'Assessment'}
            </Text>
          </View>
        </View>

        {showTimeEstimate &&
          estimatedTimeRemaining &&
          estimatedTimeRemaining > 0 && (
            <View style={styles.timeEstimate}>
              <Clock size={12} color="#6B7280" />
              <Text style={styles.timeText}>
                {formatTimeEstimate(estimatedTimeRemaining)}
              </Text>
            </View>
          )}
      </View>

      {/* Progress bar */}
      <View style={styles.progressBarContainer}>
        <View style={styles.progressBarBackground}>
          <Animated.View
            style={[
              styles.progressBarFill,
              {
                width: animatedProgress.interpolate({
                  inputRange: [0, 100],
                  outputRange: ['0%', '100%'],
                  extrapolate: 'clamp',
                }),
                backgroundColor: phaseInfo.color || '#3B82F6',
              },
            ]}
          />
        </View>
        <Text style={styles.percentageText}>
          {Math.round(progressPercentage || 0)}%
        </Text>
      </View>

      {/* Phase indicators */}
      <View style={styles.phaseIndicators}>
        {['initial', 'confirmation', 'comparison'].map((phaseName, index) => {
          const isActive = phase === phaseName;
          const isCompleted =
            (['confirmation', 'comparison', 'complete'].includes(phase) &&
              ['initial'].includes(phaseName)) ||
            (['comparison', 'complete'].includes(phase) &&
              ['confirmation'].includes(phaseName)) ||
            (phase === 'complete' && phaseName === 'comparison');

          return (
            <View key={phaseName} style={styles.phaseIndicator}>
              <View
                style={[
                  styles.phaseCircle,
                  isCompleted && styles.phaseCircleCompleted,
                  isActive && styles.phaseCircleActive,
                ]}
              >
                {isCompleted ? (
                  <CheckCircle size={12} color="#ffffff" />
                ) : (
                  <Text
                    style={[
                      styles.phaseNumber,
                      (isActive || isCompleted) && styles.phaseNumberActive,
                    ]}
                  >
                    {(index || 0) + 1}
                  </Text>
                )}
              </View>
              <Text
                style={[
                  styles.phaseLabel,
                  (isActive || isCompleted) && styles.phaseLabelActive,
                ]}
              >
                {phaseName
                  ? phaseName.charAt(0).toUpperCase() + phaseName.slice(1)
                  : 'Phase'}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  stepInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  stepText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
  },
  phaseBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  phaseText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  timeEstimate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timeText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 16,
  },
  progressBarBackground: {
    flex: 1,
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 3,
    minWidth: 2,
  },
  percentageText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    minWidth: 32,
    textAlign: 'right',
  },
  phaseIndicators: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  phaseIndicator: {
    alignItems: 'center',
    flex: 1,
  },
  phaseCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  phaseCircleActive: {
    backgroundColor: '#3B82F6',
  },
  phaseCircleCompleted: {
    backgroundColor: '#10B981',
  },
  phaseNumber: {
    fontSize: 10,
    fontWeight: '600',
    color: '#9CA3AF',
  },
  phaseNumberActive: {
    color: '#ffffff',
  },
  phaseLabel: {
    fontSize: 10,
    color: '#9CA3AF',
    textAlign: 'center',
  },
  phaseLabelActive: {
    color: '#1F2937',
    fontWeight: '500',
  },
});
