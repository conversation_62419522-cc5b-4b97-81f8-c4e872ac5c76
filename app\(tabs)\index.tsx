import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { supabase } from '@/lib/supabase';
import { openRouterService } from '@/lib/openrouter';
import {
  PersonalityProfile,
  MoodEntry,
  UserProfile,
  Subgreeting,
  PersonalityTrait,
} from '@/types/personality';
import { TEMPERAMENT_COLORS } from '@/constants/temperaments';
import {
  Brain,
  Heart,
  Lightbulb,
  Quote,
  Sparkles,
  TrendingUp,
  Users,
  MessageSquare,
  Settings,
  Plus,
  X,
  Save,
  RefreshCw,
  Smile,
  Calendar,
  Clock,
  CreditCard as Edit3,
  History,
} from 'lucide-react-native';
import NotificationSystem from '@/components/NotificationSystem';

interface DailyContent {
  insight: string;
  tip: string;
  quote: string;
  author?: string;
}

const MOOD_EMOJIS = ['😄', '🙂', '😐', '😕', '😢'];

export default function HomeScreen() {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [personalityProfile, setPersonalityProfile] =
    useState<PersonalityProfile | null>(null);
  const [currentMood, setCurrentMood] = useState<string>('😐');
  const [moodDescription, setMoodDescription] = useState<string>('');
  const [subgreeting, setSubgreeting] = useState<string>('');
  const [personalityTraits, setPersonalityTraits] = useState<
    PersonalityTrait[]
  >([]);
  const [dailyContent, setDailyContent] = useState<DailyContent | null>(null);
  const [moodHistory, setMoodHistory] = useState<MoodEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [showMoodSelector, setShowMoodSelector] = useState(false);
  const [showMoodHistory, setShowMoodHistory] = useState(false);
  const [savingMood, setSavingMood] = useState(false);
  const [regeneratingContent, setRegeneratingContent] = useState<{
    insight: boolean;
    tip: boolean;
    quote: boolean;
  }>({
    insight: false,
    tip: false,
    quote: false,
  });
  const [hiddenCards, setHiddenCards] = useState<{
    insight: boolean;
    tip: boolean;
    quote: boolean;
  }>({
    insight: false,
    tip: false,
    quote: false,
  });
  const mounted = useRef(true);

  useEffect(() => {
    mounted.current = true;
    loadUserData();

    return () => {
      mounted.current = false;
    };
  }, []);

  const loadUserData = async () => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user || !mounted.current) return;

      // Load user profile
      const { data: profile } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      // Load personality profile
      const { data: personality } = await supabase
        .from('personality_profiles')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (mounted.current) {
        setUserProfile(profile);
        setPersonalityProfile(personality);

        if (personality) {
          await loadPersonalityTraits(personality);
          await loadSubgreeting(personality);
          await loadDailyContent(personality);
        }

        // Load latest mood and mood history
        await loadLatestMood(user.id);
        await loadMoodHistory(user.id);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    } finally {
      if (mounted.current) {
        setLoading(false);
      }
    }
  };

  const loadLatestMood = async (userId: string) => {
    try {
      const { data: latestMood } = await supabase
        .from('mood_entries')
        .select('emoji, description')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (mounted.current && latestMood) {
        setCurrentMood(latestMood.emoji);
        setMoodDescription(latestMood.description || '');
      }
    } catch (error) {
      // No mood entries yet, keep default
    }
  };

  const loadMoodHistory = async (userId: string) => {
    try {
      const { data: moods } = await supabase
        .from('mood_entries')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(30); // Last 30 mood entries

      if (mounted.current && moods) {
        setMoodHistory(moods);
      }
    } catch (error) {
      console.error('Error loading mood history:', error);
    }
  };

  const loadPersonalityTraits = async (personality: PersonalityProfile) => {
    try {
      const { data: traits } = await supabase.rpc(
        'get_mixed_personality_traits',
        {
          dominant_temp: personality.dominant_temperament,
          secondary_temp: personality.secondary_temperament,
          dominant_pct: personality.dominant_percentage,
          secondary_pct: personality.secondary_percentage,
        }
      );

      if (mounted.current && traits) {
        setPersonalityTraits(traits);
      }
    } catch (error) {
      console.error('Error loading personality traits:', error);
    }
  };

  const loadSubgreeting = async (personality: PersonalityProfile) => {
    try {
      // Try AI-generated subgreeting first
      if (openRouterService.isConfigured()) {
        try {
          const aiSubgreeting =
            await openRouterService.generatePersonalizedSubgreeting({
              dominant_temperament: personality.dominant_temperament,
              secondary_temperament: personality.secondary_temperament,
              dominant_percentage: personality.dominant_percentage,
              secondary_percentage: personality.secondary_percentage,
              current_mood: currentMood,
              time_of_day: getTimeOfDay(),
              user_name: userProfile?.full_name,
            });

          if (mounted.current && aiSubgreeting.combined_message) {
            setSubgreeting(aiSubgreeting.combined_message);
            return;
          }
        } catch (aiError) {
          console.warn('AI subgreeting failed, falling back to database');
        }
      }

      // Fallback to database subgreetings
      const { data: subgreetings } = await supabase
        .from('subgreetings')
        .select('message')
        .eq('temperament', personality.dominant_temperament)
        .or('mood.eq.' + currentMood + ',mood.eq.neutral')
        .or('time_of_day.eq.' + getTimeOfDay() + ',time_of_day.eq.any')
        .order('created_at', { ascending: false });

      if (mounted.current && subgreetings && subgreetings.length > 0) {
        const randomSubgreeting =
          subgreetings[Math.floor(Math.random() * subgreetings.length)];
        setSubgreeting(randomSubgreeting.message);
      }
    } catch (error) {
      console.error('Error loading subgreeting:', error);
      if (mounted.current) {
        setSubgreeting('ready to make today amazing');
      }
    }
  };

  const loadDailyContent = async (personality: PersonalityProfile) => {
    try {
      // Try AI-generated content first
      if (openRouterService.isConfigured()) {
        try {
          const [aiInsight, aiTip, aiQuote] = await Promise.all([
            openRouterService.generatePersonalizedInsight({
              temperament: personality.dominant_temperament,
              secondary_temperament: personality.secondary_temperament,
              current_mood: currentMood,
              insight_type: 'insight',
            }),
            openRouterService.generatePersonalizedInsight({
              temperament: personality.dominant_temperament,
              secondary_temperament: personality.secondary_temperament,
              current_mood: currentMood,
              insight_type: 'tip',
            }),
            openRouterService.generatePersonalizedInsight({
              temperament: personality.dominant_temperament,
              secondary_temperament: personality.secondary_temperament,
              current_mood: currentMood,
              insight_type: 'quote',
            }),
          ]);

          if (mounted.current) {
            setDailyContent({
              insight: aiInsight.content,
              tip: aiTip.content,
              quote: aiQuote.content,
              author: 'Harmona AI',
            });
            return;
          }
        } catch (aiError) {
          console.warn(
            'AI content generation failed, falling back to database'
          );
        }
      }

      // Fallback to database content
      const [insightResult, tipResult, quoteResult] = await Promise.all([
        supabase.rpc('get_personality_quote', {
          user_temperament: personality.dominant_temperament,
          user_mood: currentMood,
          time_context: getTimeOfDay(),
        }),
        supabase.rpc('get_personality_tip', {
          user_temperament: personality.dominant_temperament,
          user_mood: currentMood,
          time_context: getTimeOfDay(),
        }),
        supabase.rpc('get_personality_quote', {
          user_temperament: personality.dominant_temperament,
          user_mood: currentMood,
          time_context: getTimeOfDay(),
        }),
      ]);

      if (mounted.current) {
        setDailyContent({
          insight:
            insightResult.data?.[0]?.quote ||
            'Your unique perspective brings value to every situation.',
          tip:
            tipResult.data?.[0]?.tip ||
            'Take a moment today to appreciate your natural strengths.',
          quote:
            quoteResult.data?.[0]?.quote ||
            'Every day is a new opportunity to grow and connect.',
          author: quoteResult.data?.[0]?.author || 'Harmona',
        });
      }
    } catch (error) {
      console.error('Error loading daily content:', error);
      if (mounted.current) {
        setDailyContent({
          insight: 'Your unique perspective brings value to every situation.',
          tip: 'Take a moment today to appreciate your natural strengths.',
          quote: 'Every day is a new opportunity to grow and connect.',
          author: 'Harmona',
        });
      }
    }
  };

  const getTimeOfDay = (): 'morning' | 'afternoon' | 'evening' => {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 18) return 'afternoon';
    return 'evening';
  };

  const handleMoodSelect = async (emoji: string) => {
    if (savingMood) return;

    setCurrentMood(emoji);
    setShowMoodSelector(false);
  };

  const handleSaveMood = async () => {
    if (savingMood) return;

    setSavingMood(true);

    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) return;

      // Save mood to database
      const { error } = await supabase.from('mood_entries').insert({
        user_id: user.id,
        emoji: currentMood,
        description: moodDescription.trim(),
      });

      if (error) throw error;

      // Reload content based on new mood
      if (personalityProfile) {
        await loadSubgreeting(personalityProfile);
        await loadDailyContent(personalityProfile);
      }

      // Reload mood history
      await loadMoodHistory(user.id);

      Alert.alert('Saved!', 'Your mood has been recorded.');
    } catch (error) {
      console.error('Error saving mood:', error);
      Alert.alert('Error', 'Failed to save mood. Please try again.');
    } finally {
      setSavingMood(false);
    }
  };

  const handleSaveContent = async (
    type: 'insight' | 'tip' | 'quote',
    content: string
  ) => {
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user || !personalityProfile) return;

      const { error } = await supabase.from('saved_insights').insert({
        user_id: user.id,
        insight_type: type,
        content: content,
        temperament: personalityProfile.dominant_temperament,
      });

      if (error) throw error;

      Alert.alert(
        'Saved!',
        type.charAt(0).toUpperCase() +
          type.slice(1) +
          ' saved to your collection.'
      );
    } catch (error) {
      console.error('Error saving content:', error);
      Alert.alert('Error', 'Failed to save content. Please try again.');
    }
  };

  const handleRegenerateContent = async (type: 'insight' | 'tip' | 'quote') => {
    if (!personalityProfile || regeneratingContent[type]) return;

    setRegeneratingContent((prev) => ({ ...prev, [type]: true }));

    try {
      let newContent = '';

      // Try AI generation first
      if (openRouterService.isConfigured()) {
        try {
          const aiContent = await openRouterService.generatePersonalizedInsight(
            {
              temperament: personalityProfile.dominant_temperament,
              secondary_temperament: personalityProfile.secondary_temperament,
              current_mood: currentMood,
              insight_type: type,
            }
          );
          newContent = aiContent.content;
        } catch (aiError) {
          console.warn('AI regeneration failed, falling back to database');
        }
      }

      // Fallback to database
      if (!newContent) {
        if (type === 'quote') {
          const { data } = await supabase.rpc('get_personality_quote', {
            user_temperament: personalityProfile.dominant_temperament,
            user_mood: currentMood,
            time_context: getTimeOfDay(),
          });
          newContent = data?.[0]?.quote || 'Every moment is a chance to grow.';
        } else {
          const { data } = await supabase.rpc('get_personality_tip', {
            user_temperament: personalityProfile.dominant_temperament,
            user_mood: currentMood,
            time_context: getTimeOfDay(),
          });
          newContent =
            data?.[0]?.tip || 'Trust in your natural abilities today.';
        }
      }

      // Update the specific content
      setDailyContent((prev) =>
        prev
          ? {
              ...prev,
              [type]: newContent,
            }
          : null
      );
    } catch (error) {
      console.error('Error regenerating content:', error);
      Alert.alert('Error', 'Failed to regenerate content. Please try again.');
    } finally {
      setRegeneratingContent((prev) => ({ ...prev, [type]: false }));
    }
  };

  const handleCloseCard = (type: 'insight' | 'tip' | 'quote') => {
    setHiddenCards((prev) => ({ ...prev, [type]: true }));
  };

  const getTemperamentColor = (temperament: string) => {
    return (
      TEMPERAMENT_COLORS[temperament as keyof typeof TEMPERAMENT_COLORS] ||
      '#6B7280'
    );
  };

  const formatTemperamentName = (temperament: string): string => {
    return temperament.charAt(0).toUpperCase() + temperament.slice(1);
  };

  const formatMoodDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return diffInHours + 'h ago';
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays === 1) {
        return 'Yesterday';
      } else if (diffInDays < 7) {
        return diffInDays + ' days ago';
      } else {
        return date.toLocaleDateString();
      }
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={styles.loadingText}>
            Loading your personalized experience...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!personalityProfile) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.emptyContainer}>
          <Brain size={64} color="#D1D5DB" />
          <Text style={styles.emptyTitle}>Complete Your Assessment</Text>
          <Text style={styles.emptyText}>
            Take our personality assessment to unlock personalized insights and
            daily guidance.
          </Text>
          <TouchableOpacity
            style={styles.assessmentButton}
            onPress={() => router.push('/assessment')}
          >
            <Text style={styles.assessmentButtonText}>Start Assessment</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={styles.greeting}>
              Good {getTimeOfDay()},{' '}
              {userProfile?.full_name?.split(' ')[0] || 'there'}!
            </Text>
            <Text style={styles.subgreeting}>You're {subgreeting}</Text>
          </View>
          <NotificationSystem
            onNavigate={(route) => router.push(route as any)}
          />
        </View>

        {/* Mood Meter */}
        <View style={styles.moodSection}>
          <View style={styles.moodHeader}>
            <Text style={styles.sectionTitle}>How are you feeling?</Text>
            <TouchableOpacity
              style={styles.historyButton}
              onPress={() => setShowMoodHistory(true)}
            >
              <History size={16} color="#6B7280" />
              <Text style={styles.historyButtonText}>History</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.moodContainer}>
            <TouchableOpacity
              style={styles.currentMoodButton}
              onPress={() => setShowMoodSelector(!showMoodSelector)}
              disabled={savingMood}
            >
              <Text style={styles.currentMoodEmoji}>{currentMood}</Text>
              <Text style={styles.currentMoodText}>
                {savingMood ? 'Saving...' : 'Tap to change'}
              </Text>
            </TouchableOpacity>

            {showMoodSelector && (
              <View style={styles.moodSelector}>
                {MOOD_EMOJIS.map((emoji) => (
                  <TouchableOpacity
                    key={emoji}
                    style={[
                      styles.moodOption,
                      currentMood === emoji && styles.selectedMoodOption,
                    ]}
                    onPress={() => handleMoodSelect(emoji)}
                  >
                    <Text style={styles.moodOptionEmoji}>{emoji}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* Mood Description Input */}
            <View style={styles.moodInputContainer}>
              <TextInput
                style={styles.moodTextInput}
                placeholder="How are you feeling? (optional)"
                value={moodDescription}
                onChangeText={setMoodDescription}
                multiline
                maxLength={200}
                numberOfLines={3}
              />
              <TouchableOpacity
                style={[
                  styles.saveMoodButton,
                  savingMood && styles.saveMoodButtonDisabled,
                ]}
                onPress={handleSaveMood}
                disabled={savingMood}
              >
                {savingMood ? (
                  <ActivityIndicator size={16} color="#ffffff" />
                ) : (
                  <Save size={16} color="#ffffff" />
                )}
                <Text style={styles.saveMoodButtonText}>
                  {savingMood ? 'Saving...' : 'Save Mood'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Personality Overview */}
        <View style={styles.personalitySection}>
          <Text style={styles.sectionTitle}>Your Personality</Text>
          <View style={styles.personalityCard}>
            <View style={styles.temperamentRow}>
              <View style={styles.temperamentItem}>
                <View
                  style={[
                    styles.temperamentDot,
                    {
                      backgroundColor: getTemperamentColor(
                        personalityProfile.dominant_temperament
                      ),
                    },
                  ]}
                />
                <Text style={styles.temperamentText}>
                  {formatTemperamentName(
                    personalityProfile.dominant_temperament
                  )}{' '}
                  ({personalityProfile.dominant_percentage}%)
                </Text>
              </View>
              <View style={styles.temperamentItem}>
                <View
                  style={[
                    styles.temperamentDot,
                    {
                      backgroundColor: getTemperamentColor(
                        personalityProfile.secondary_temperament
                      ),
                    },
                  ]}
                />
                <Text style={styles.temperamentText}>
                  {formatTemperamentName(
                    personalityProfile.secondary_temperament
                  )}{' '}
                  ({personalityProfile.secondary_percentage}%)
                </Text>
              </View>
            </View>

            {personalityTraits.length > 0 && (
              <View style={styles.traitsContainer}>
                {personalityTraits.slice(0, 5).map((trait, index) => (
                  <View key={index} style={styles.traitTag}>
                    <Text style={styles.traitText}>{trait.trait_name}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>

        {/* Daily Dose */}
        <View style={styles.dailyDoseSection}>
          <Text style={styles.sectionTitle}>Your Daily Dose</Text>

          {/* Insight Card */}
          {dailyContent && !hiddenCards.insight && (
            <View style={styles.contentCard}>
              <View style={styles.contentHeader}>
                <View style={styles.contentTypeContainer}>
                  <Lightbulb size={20} color="#F59E0B" />
                  <Text style={styles.contentType}>Daily Insight</Text>
                </View>
                <View style={styles.contentActions}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() =>
                      handleSaveContent('insight', dailyContent.insight)
                    }
                  >
                    <Save size={16} color="#10B981" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleRegenerateContent('insight')}
                    disabled={regeneratingContent.insight}
                  >
                    {regeneratingContent.insight ? (
                      <ActivityIndicator size={16} color="#3B82F6" />
                    ) : (
                      <RefreshCw size={16} color="#3B82F6" />
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleCloseCard('insight')}
                  >
                    <X size={16} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </View>
              <Text style={styles.contentText}>{dailyContent.insight}</Text>
            </View>
          )}

          {/* Tip Card */}
          {dailyContent && !hiddenCards.tip && (
            <View style={styles.contentCard}>
              <View style={styles.contentHeader}>
                <View style={styles.contentTypeContainer}>
                  <MessageSquare size={20} color="#10B981" />
                  <Text style={styles.contentType}>Daily Tip</Text>
                </View>
                <View style={styles.contentActions}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleSaveContent('tip', dailyContent.tip)}
                  >
                    <Save size={16} color="#10B981" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleRegenerateContent('tip')}
                    disabled={regeneratingContent.tip}
                  >
                    {regeneratingContent.tip ? (
                      <ActivityIndicator size={16} color="#3B82F6" />
                    ) : (
                      <RefreshCw size={16} color="#3B82F6" />
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleCloseCard('tip')}
                  >
                    <X size={16} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </View>
              <Text style={styles.contentText}>{dailyContent.tip}</Text>
            </View>
          )}

          {/* Quote Card */}
          {dailyContent && !hiddenCards.quote && (
            <View style={styles.contentCard}>
              <View style={styles.contentHeader}>
                <View style={styles.contentTypeContainer}>
                  <Quote size={20} color="#8B5CF6" />
                  <Text style={styles.contentType}>Daily Quote</Text>
                </View>
                <View style={styles.contentActions}>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() =>
                      handleSaveContent('quote', dailyContent.quote)
                    }
                  >
                    <Save size={16} color="#10B981" />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleRegenerateContent('quote')}
                    disabled={regeneratingContent.quote}
                  >
                    {regeneratingContent.quote ? (
                      <ActivityIndicator size={16} color="#3B82F6" />
                    ) : (
                      <RefreshCw size={16} color="#3B82F6" />
                    )}
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.actionButton}
                    onPress={() => handleCloseCard('quote')}
                  >
                    <X size={16} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </View>
              <Text style={styles.contentText}>"{dailyContent.quote}"</Text>
              {dailyContent.author && (
                <Text style={styles.contentAuthor}>
                  — {dailyContent.author}
                </Text>
              )}
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => router.push('/(tabs)/compare')}
            >
              <TrendingUp size={24} color="#3B82F6" />
              <Text style={styles.quickActionText}>Compare</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => router.push('/(tabs)/circle')}
            >
              <Users size={24} color="#10B981" />
              <Text style={styles.quickActionText}>Circle</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => router.push('/(tabs)/talk')}
            >
              <MessageSquare size={24} color="#8B5CF6" />
              <Text style={styles.quickActionText}>AI Chat</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.quickActionCard}
              onPress={() => router.push('/(tabs)/settings')}
            >
              <Settings size={24} color="#6B7280" />
              <Text style={styles.quickActionText}>Settings</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Mood History Modal */}
      <Modal
        visible={showMoodHistory}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMoodHistory(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Mood History</Text>
              <TouchableOpacity onPress={() => setShowMoodHistory(false)}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.moodHistoryList}
              showsVerticalScrollIndicator={false}
            >
              {moodHistory.length === 0 ? (
                <View style={styles.emptyMoodHistory}>
                  <Smile size={48} color="#D1D5DB" />
                  <Text style={styles.emptyMoodTitle}>No mood history yet</Text>
                  <Text style={styles.emptyMoodText}>
                    Start tracking your moods to see patterns and insights over
                    time
                  </Text>
                </View>
              ) : (
                moodHistory.map((mood) => (
                  <View key={mood.id} style={styles.moodHistoryItem}>
                    <View style={styles.moodHistoryEmoji}>
                      <Text style={styles.moodHistoryEmojiText}>
                        {mood.emoji}
                      </Text>
                    </View>
                    <View style={styles.moodHistoryContent}>
                      <View style={styles.moodHistoryHeader}>
                        <Text style={styles.moodHistoryDate}>
                          {formatMoodDate(mood.created_at)}
                        </Text>
                        <Text style={styles.moodHistoryTime}>
                          {new Date(mood.created_at).toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit',
                          })}
                        </Text>
                      </View>
                      {mood.description && (
                        <Text style={styles.moodHistoryDescription}>
                          {mood.description}
                        </Text>
                      )}
                    </View>
                  </View>
                ))
              )}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    gap: 16,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  assessmentButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  assessmentButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  headerContent: {
    flex: 1,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  subgreeting: {
    fontSize: 16,
    color: '#6B7280',
  },
  moodSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  moodHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  historyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
  },
  historyButtonText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  moodContainer: {
    alignItems: 'center',
    gap: 16,
  },
  currentMoodButton: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    minWidth: 120,
  },
  currentMoodEmoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  currentMoodText: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  moodSelector: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    gap: 4,
  },
  moodOption: {
    padding: 12,
    borderRadius: 12,
    minWidth: 48,
    alignItems: 'center',
  },
  selectedMoodOption: {
    backgroundColor: '#EBF8FF',
  },
  moodOptionEmoji: {
    fontSize: 24,
  },
  moodInputContainer: {
    width: '100%',
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    gap: 12,
  },
  moodTextInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
    textAlignVertical: 'top',
    minHeight: 80,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  saveMoodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    paddingVertical: 12,
    borderRadius: 12,
    gap: 8,
  },
  saveMoodButtonDisabled: {
    opacity: 0.6,
  },
  saveMoodButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#ffffff',
  },
  personalitySection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  personalityCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  temperamentRow: {
    gap: 12,
    marginBottom: 16,
  },
  temperamentItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  temperamentDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  temperamentText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  traitsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  traitTag: {
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  traitText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  dailyDoseSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  contentCard: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  contentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  contentTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  contentType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  contentActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F9FAFB',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  contentText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
  },
  contentAuthor: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
    marginTop: 8,
    textAlign: 'right',
  },
  quickActionsSection: {
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  quickActionCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#ffffff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 40,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
  },
  moodHistoryList: {
    maxHeight: 500,
  },
  emptyMoodHistory: {
    alignItems: 'center',
    paddingVertical: 40,
    gap: 12,
  },
  emptyMoodTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6B7280',
  },
  emptyMoodText: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 20,
  },
  moodHistoryItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    gap: 12,
  },
  moodHistoryEmoji: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moodHistoryEmojiText: {
    fontSize: 24,
  },
  moodHistoryContent: {
    flex: 1,
  },
  moodHistoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  moodHistoryDate: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
  },
  moodHistoryTime: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  moodHistoryDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
});
